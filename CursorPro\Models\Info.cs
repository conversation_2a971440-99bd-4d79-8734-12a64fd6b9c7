using System.Runtime.InteropServices;

namespace CursorPro;

//本地配置
public static class Info
{
    public static string AppName = "CursorPro";
    public static string 本地版本号 = "2.2.1";
    public static string 机器码 = "";

    // API基础地址（不含端口）
    public static string API基础地址 = "http://14.103.190.198";

    // 不同服务的端口配置
    public static string 免费服务端口 = "5269";  // 免费/试用服务端口
    public static string 付费服务端口 = "5270";  // 付费验证服务端口
    public static string 支付服务端口 = "5001";  // 支付服务端口

    // 向后兼容的API地址（供旧代码使用）
    public static string API地址 => $"{API基础地址}:{免费服务端口}";

    public static string 到期时间 = "";
    public static string 平台类型 = RuntimeInformation.IsOSPlatform(OSPlatform.Windows) ? "Windows" : "Mac";

    //额度检测
    public static int 剩余额度 = 0;

    // 代理相关属性
    public static string 代理 = "官方版";   //用于区分不同代理 官方版

    // 下载链接配置
    public static string Windows下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";
    public static string Mac下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";
    public static string 默认下载链接 = "https://wwcc.lanzoue.com/b00od5g23a";

    /// <summary>
    /// 根据当前平台获取对应的下载链接
    /// </summary>
    public static string 获取下载链接()
    {
        return 平台类型 switch
        {
            "Windows" => Windows下载链接,
            "Mac" => Mac下载链接,
            _ => 默认下载链接
        };
    }
}



