using Avalonia;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CursorPro.Models;
using MsBox.Avalonia.Enums;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Net.Http;
using System.Text;
using System.Text.Json;

namespace CursorPro.ViewModels
{
    public partial class 主要窗口 : ViewModelBase, IDisposable
    {
        private readonly 检查限制 _检查限制 = new();

        private readonly 重置机器码 _重置机器码 = new();

        [ObservableProperty]
        private bool _显示功能按钮 = true;

        [ObservableProperty]
        private string _获取状态文本 = string.Empty;

        [ObservableProperty]
        private string _使用类型文本 = string.Empty;

        [ObservableProperty]
        private string _版本号 = $"v{Info.本地版本号}";

        [ObservableProperty]
        private bool _加速Cursor已启用 = false;

        private bool _显示加速Cursor功能 = false;

        /// <summary>
        /// 显示加速Cursor功能
        /// </summary>
        public bool 显示加速Cursor功能
        {
            get => _显示加速Cursor功能;
            set
            {
                if (SetProperty(ref _显示加速Cursor功能, value))
                {
                    OnPropertyChanged(nameof(版本链接边距));
                }
            }
        }

        /// <summary>
        /// 版本链接的边距，根据加速功能显示状态动态调整
        /// </summary>
        public Thickness 版本链接边距 => 显示加速Cursor功能 ? new Thickness(0, -8, 0, 0) : new Thickness(0, 17, 0, 0);

        // 获取到期时间，避免重复读取配置
        private DateTime? 获取到期时间()
        {
            string? expiryTimeStr = LocalConfig.读取("ExpiryTime");
            if (!string.IsNullOrEmpty(expiryTimeStr) && DateTime.TryParse(expiryTimeStr, out DateTime expiryTime))
            {
                return expiryTime;
            }
            return null;
        }

        // 使用辅助方法获取到期时间
        private bool 是付费用户未到期
        {
            get
            {
                var expiryTime = 获取到期时间();
                return expiryTime.HasValue && expiryTime.Value > DateTime.Now;
            }
        }

        private bool 是付费用户已到期
        {
            get
            {
                var expiryTime = 获取到期时间();
                return expiryTime.HasValue && expiryTime.Value <= DateTime.Now;
            }
        }

        [ObservableProperty]
        private string _窗口标题 = string.Empty;

        public 主要窗口()
        {
            // 窗口标题始终保持为空
            窗口标题 = string.Empty;

            // 初始化时获取并缓存Cursor路径
            CursorProcessManager.InitializeCursorPathCache();

            // 初始化加速Cursor状态（从配置文件读取）
            初始化加速Cursor状态();

            // 更新使用类型文本
            更新使用类型文本();

            // 初始化获取状态文本，显示到期时间
            更新获取状态文本();
        }

        /// <summary>
        /// 根据付费状态更新使用类型文本
        /// </summary>
        public void 更新使用类型文本()
        {
            try
            {
                // 使用辅助方法获取到期时间，避免重复读取配置
                var 到期时间 = 获取到期时间();

                if (到期时间.HasValue)
                {
                    if (到期时间.Value > DateTime.Now)
                    {
                        // 付费未到期用户，显示剩余天数
                        int 剩余天数 = (int)Math.Ceiling((到期时间.Value - DateTime.Now).TotalDays);

                        // 如果剩余天数低于3天，显示续费提示
                        if (剩余天数 < 3)
                        {
                            使用类型文本 = $"独享不足{剩余天数}天，是否续费？";
                        }
                        else
                        {
                            使用类型文本 = $"独享剩余 {剩余天数} 天";
                        }
                    }
                    else
                    {
                        // 付费已到期用户，显示已到期
                        使用类型文本 = "独享已到期，是否续费？";
                    }
                }
                else
                {
                    // 免费用户或无法解析到期时间
                    使用类型文本 = "免费使用中";
                }

                // 更新加速功能显示状态（关键修复）
                初始化加速Cursor状态();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"更新使用类型文本时出错: {ex.Message}");
                使用类型文本 = "免费使用中";
            }
        }

        /// <summary>
        /// 更新获取状态文本，只用于显示获取成功或失败等临时状态
        /// </summary>
        private void 更新获取状态文本()
        {
            // 状态文本不再显示到期信息，置空
            获取状态文本 = "";
        }

        [RelayCommand]
        private async Task 一键获取快速额度Async()
        {
            try
            {
                // 更新使用类型文本
                更新使用类型文本();

                // 暂时清空获取状态文本，操作完成后会更新
                获取状态文本 = string.Empty;

                // 使用缓存的Cursor路径（性能优化）
                string? cursorPath = CursorProcessManager.GetCachedCursorPath();
                if (string.IsNullOrEmpty(cursorPath))
                {
                    Debug.WriteLine("[ERROR] 未检测到Cursor路径，终止一键获取操作");
                    获取状态文本 = "获取失败";
                    await Tools.显示消息框Async("提示", "请先启动Cursor再获取", ButtonEnum.Ok, Icon.Warning);
                    return;
                }

                Debug.WriteLine($"[INFO] 使用Cursor路径: {cursorPath}");

                // 移除本地虚假获取检查，改为服务端控制

                // 直接进行权限检查
                var 验证结果 = await _检查限制.启动检查();

                // 如果验证失败，直接判断是否为免费限制，简化购买流程
                if (!验证结果.Success)
                {
                    bool 是免费限制 = 是否免费限制问题(验证结果.Message);

                    if (是免费限制)
                    {
                        Debug.WriteLine("检测到免费额度限制，直接引导至购买");

                        // 在弹出购买对话框之前清除代理设置
                        bool 清除结果 = _重置机器码.DeleteProxySettings();
                        Debug.WriteLine($"[INFO] 购买前清除代理设置结果: {清除结果}");

                        // 显示服务器的原话并询问是否打开购买窗口
                        var 结果 = await Tools.显示确认消息框Async(
                            "温馨提示",
                            验证结果.Message,
                            ButtonEnum.YesNo,
                            Icon.Info);

                        if (结果 == ButtonResult.Yes)
                        {
                            购买卡密VM.显示购买窗口();
                        }
                    }
                    else
                    {
                        // 其他错误，只显示错误消息
                        await Tools.显示消息框Async("温馨提示", 验证结果.Message, ButtonEnum.Ok, Icon.Warning);
                    }
                    return;
                }

                // 验证成功，检查是否为虚假获取
                if (验证结果.IsFake == true)
                {
                    // 虚假获取，根据用户类型执行对应的虚假获取逻辑
                    Debug.WriteLine("服务端返回虚假获取，执行虚假获取逻辑");

                    if (免费虚假获取.是否为免费用户())
                    {
                        Debug.WriteLine("执行免费虚假获取");
                        await 免费虚假获取.执行免费虚假获取(text => 获取状态文本 = text);
                    }
                    else
                    {
                        Debug.WriteLine("执行付费虚假获取");
                        await 免费虚假获取.执行付费虚假获取(text => 获取状态文本 = text);
                    }
                }
                else if (验证结果.AccountInfo != null)
                {
                    // 真实获取成功，处理账号信息
                    var 邮箱 = 验证结果.AccountInfo.邮箱;
                    var 访问令牌 = 验证结果.AccountInfo.访问令牌;
                    var 刷新令牌 = 验证结果.AccountInfo.刷新令牌;

                    Debug.WriteLine($"获取到账号: {邮箱}");

                    // 重置机器码并写入认证数据（统一操作）
                    bool 重置成功 = await _重置机器码.执行重置机器码(邮箱, 访问令牌, 刷新令牌);

                    if (重置成功)
                    {
                        // 显示获取成功信息
                        获取状态文本 = "获取成功! 和cursor继续对话吧";
                        Debug.WriteLine("真实获取成功");

                        // 更新加速功能可见性（用户现在有账号了）
                        显示加速Cursor功能 = true;
                        Debug.WriteLine("[INFO] 用户获取成功，显示加速功能");
                    }
                }
                else
                {
                    // 账号信息为空
                    获取状态文本 = "获取失败";
                    await Tools.显示消息框Async("提示", "无法获取账号信息，请稍后重试", ButtonEnum.Ok, Icon.Warning);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"一键获取快速额度时发生错误: {ex.Message}");
                获取状态文本 = "获取失败";
                await Tools.显示消息框Async("提示", "获取额度失败，请稍后重试", ButtonEnum.Ok, Icon.Warning);
            }
            finally
            {
                // 强制清理SQLite连接池 - 这是解决数据库锁定的关键
                Microsoft.Data.Sqlite.SqliteConnection.ClearAllPools();
                Debug.WriteLine("一键获取额度完成，已清理SQLite连接池");

                // 无论成功还是失败，都更新使用类型文本
                更新使用类型文本();
            }
        }

        /// <summary>
        /// 判断是否为免费限制问题（用于简化购买引导）
        /// </summary>
        private bool 是否免费限制问题(string 错误消息)
        {
            if (string.IsNullOrEmpty(错误消息))
                return false;

            // 免费限制关键词
            var 免费限制关键词 = new[]
            {
                "到期",      // 卡密过期相关
                "过期",      // 卡密过期相关
                "紧张",      // 资源不足
                "上限",      // 免费获取次数达到上限
                "重新购买",   // 需要重新购买额度
                "次数",      // 与使用次数相关
                "限额",      // 与限额相关
                "共享",      // 与共享资源相关
                "不足",       // 资源不足相关
                "用完"       // 资源用完相关
            };

            foreach (var 关键词 in 免费限制关键词)
            {
                if (错误消息.Contains(关键词, StringComparison.OrdinalIgnoreCase))
                {
                    Debug.WriteLine($"匹配免费限制关键词: {关键词}，错误消息: {错误消息}");
                    return true;
                }
            }

            return false;
        }

        [RelayCommand]
        private void 使用教程()
        {
            // 打开使用教程链接
            Tools.OpenUrl("https://hiioj3vbs6o.feishu.cn/wiki/JGtHwp6fyif07vkHjQNcQEV6nwN?from=from_copylink");
        }

        [RelayCommand]
        private void 版本链接()
        {
            // 打开下载地址
            Tools.OpenUrl("https://wwcc.lanzoue.com/b00od5g23a");
        }

        [RelayCommand]
        private void 状态文本点击()
        {
            try
            {
                Debug.WriteLine("用户点击了状态文本，直接打开购买窗口");

                // 直接打开购买窗口，不再显示确认弹窗
                购买卡密VM.显示购买窗口();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"状态文本点击处理出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化加速Cursor状态
        /// </summary>
        private async void 初始化加速Cursor状态()
        {
            try
            {
                // 检查是否有卡密来决定是否显示加速功能
                string? 本地卡密 = LocalConfig.读取("CardKey");
                bool 有卡密 = !string.IsNullOrEmpty(本地卡密);

                显示加速Cursor功能 = 有卡密;
                Debug.WriteLine($"[INFO] 用户卡密状态: {(有卡密 ? "有卡密" : "无卡密")}, 显示加速功能: {显示加速Cursor功能}");

                if (有卡密)
                {
                    // 读取本地保存的加速状态
                    string? 加速状态 = LocalConfig.读取("AccelerateCursor");
                    bool 本地已启用 = !string.IsNullOrEmpty(加速状态) && bool.TryParse(加速状态, out bool result) && result;

                    if (本地已启用)
                    {
                        // 如果本地记录为已启用，需要验证服务端权限
                        Debug.WriteLine("[INFO] 检测到本地加速状态为启用，验证服务端权限...");
                        await 验证并应用加速状态Async(true);
                    }
                    else
                    {
                        加速Cursor已启用 = false;
                        Debug.WriteLine("[INFO] 本地加速状态为禁用");
                    }
                }
                else
                {
                    // 免费用户默认关闭加速功能
                    加速Cursor已启用 = false;
                    Debug.WriteLine("[INFO] 免费用户，加速功能默认关闭且不显示");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 初始化加速Cursor状态失败: {ex.Message}");
                显示加速Cursor功能 = false;
                加速Cursor已启用 = false;
            }
        }

        /// <summary>
        /// 验证并应用加速状态（启动时静默验证）
        /// </summary>
        private async Task 验证并应用加速状态Async(bool 静默模式 = false)
        {
            try
            {
                string? 本地卡密 = LocalConfig.读取("CardKey");
                if (string.IsNullOrEmpty(本地卡密))
                {
                    Debug.WriteLine("[ERROR] 本地卡密为空，无法验证加速权限");
                    加速Cursor已启用 = false;
                    LocalConfig.写入("AccelerateCursor", "false");
                    return;
                }

                // 调用API验证权限
                var 验证结果 = await 调用加速权限验证API(本地卡密);

                if (验证结果.success)
                {
                    // 有权限，应用加速配置
                    bool 应用结果 = await 应用加速配置Async(验证结果.proxyServer);
                    if (应用结果)
                    {
                        加速Cursor已启用 = true;
                        LocalConfig.写入("AccelerateCursor", "true");
                        Debug.WriteLine("[INFO] 加速权限验证成功，已应用配置");

                        // if (!静默模式)
                        // {
                        //     await Tools.显示消息框Async("提示", "已开启突破限制", ButtonEnum.Ok, Icon.Info);
                        // }
                    }
                    else
                    {
                        加速Cursor已启用 = false;
                        LocalConfig.写入("AccelerateCursor", "false");
                        Debug.WriteLine("[ERROR] 应用加速配置失败");

                        if (!静默模式)
                        {
                            await Tools.显示消息框Async("错误", "应用加速配置失败", ButtonEnum.Ok, Icon.Error);
                        }
                    }
                }
                else
                {
                    // 权限验证失败，清理状态
                    加速Cursor已启用 = false;
                    LocalConfig.写入("AccelerateCursor", "false");
                    清理加速配置();

                    Debug.WriteLine($"[INFO] 加速权限验证失败: {验证结果.message}");

                    // 静默模式下不弹出对话框
                    if (!静默模式)
                    {
                        await 处理权限验证失败(验证结果.message);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 验证加速权限失败: {ex.Message}");
                加速Cursor已启用 = false;
                LocalConfig.写入("AccelerateCursor", "false");

                if (!静默模式)
                {
                    await Tools.显示消息框Async("错误", "网络连接错误，请稍后重试", ButtonEnum.Ok, Icon.Error);
                }
            }
        }

        /// <summary>
        /// 加速Cursor切换命令（只有付费用户可见此功能）
        /// </summary>
        [RelayCommand]
        private async Task 加速Cursor切换Async()
        {
            try
            {
                Debug.WriteLine($"[INFO] 用户切换加速Cursor状态: {加速Cursor已启用}");

                if (加速Cursor已启用)
                {
                    // 启用加速：验证权限并应用配置
                    await 验证并应用加速状态Async(false);
                }
                else
                {
                    // 禁用加速：直接清理配置
                    清理加速配置();
                    LocalConfig.写入("AccelerateCursor", "false");
                    Debug.WriteLine("[INFO] 已禁用网络加速");
                    // await Tools.显示消息框Async("提示", "已关闭突破限制", ButtonEnum.Ok, Icon.Info);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 加速Cursor切换失败: {ex.Message}");
                await Tools.显示消息框Async("错误", "切换加速设置时发生错误", ButtonEnum.Ok, Icon.Error);
            }
        }

        /// <summary>
        /// 调用加速权限验证API
        /// </summary>
        private async Task<(bool success, string message, string proxyServer)> 调用加速权限验证API(string 卡密)
        {
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                var requestData = new { key = 卡密 };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var apiUrl = $"{Info.API基础地址}:{Info.付费服务端口}/api/get_proxy";
                Debug.WriteLine($"[INFO] 调用加速权限验证API: {apiUrl}");
                var response = await client.PostAsync(apiUrl, content);
                var responseText = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"[INFO] API响应: {responseText}");

                var result = JsonSerializer.Deserialize<JsonElement>(responseText);

                if (result.TryGetProperty("success", out var successProp) && successProp.GetBoolean())
                {
                    // 成功获取代理服务器
                    string proxyServer = "";
                    if (result.TryGetProperty("proxy_server", out var proxyProp))
                    {
                        proxyServer = proxyProp.GetString() ?? "";
                    }

                    return (true, "成功", proxyServer);
                }
                else
                {
                    // 获取错误信息
                    string message = "未知错误";
                    if (result.TryGetProperty("message", out var messageProp))
                    {
                        message = messageProp.GetString() ?? "未知错误";
                    }

                    return (false, message, "");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] API调用异常: {ex.Message}");
                return (false, "网络连接失败", "");
            }
        }

        /// <summary>
        /// 应用加速配置到settings.json
        /// </summary>
        private Task<bool> 应用加速配置Async(string proxyServer)
        {
            try
            {
                if (string.IsNullOrEmpty(proxyServer))
                {
                    Debug.WriteLine("[ERROR] 代理服务器地址为空");
                    return Task.FromResult(false);
                }

                // 使用重置机器码类的方法，传入动态代理服务器地址
                bool result = _重置机器码.ModifySettingsJson(proxyServer);
                Debug.WriteLine($"[INFO] 应用加速配置结果: {result}");
                return Task.FromResult(result);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 应用加速配置失败: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 清理加速配置
        /// </summary>
        private void 清理加速配置()
        {
            try
            {
                bool result = _重置机器码.DeleteProxySettings();
                Debug.WriteLine($"[INFO] 清理加速配置结果: {result}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 清理加速配置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理权限验证失败
        /// </summary>
        private async Task 处理权限验证失败(string errorMessage)
        {
            try
            {
                // 检查是否需要购买（包含"购买后"或"已过期"）
                bool 需要购买 = errorMessage.Contains("购买后") || errorMessage.Contains("已过期");

                if (需要购买)
                {
                    // 弹出购买确认对话框
                    var result = await Tools.显示确认消息框Async("提示", errorMessage, ButtonEnum.YesNo, Icon.Info);

                    if (result == ButtonResult.Yes)
                    {
                        // 用户同意购买，调用购买API
                        await 调用购买APIAsync();
                    }
                }
                else
                {
                    // 其他错误，只显示确认框
                    await Tools.显示消息框Async("提示", errorMessage, ButtonEnum.Ok, Icon.Info);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 处理权限验证失败异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 调用购买API
        /// </summary>
        private async Task 调用购买APIAsync()
        {
            try
            {
                string? 本地卡密 = LocalConfig.读取("CardKey");
                if (string.IsNullOrEmpty(本地卡密))
                {
                    await Tools.显示消息框Async("错误", "卡密信息缺失", ButtonEnum.Ok, Icon.Error);
                    return;
                }

                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(10);

                var requestData = new { card_key = 本地卡密 };
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                var apiUrl = $"{Info.API基础地址}:{Info.支付服务端口}/create_proxy_payment";
                Debug.WriteLine($"[INFO] 调用购买API: {apiUrl}");
                var response = await client.PostAsync(apiUrl, content);
                var responseText = await response.Content.ReadAsStringAsync();

                Debug.WriteLine($"[INFO] 购买API响应: {responseText}");

                var result = JsonSerializer.Deserialize<JsonElement>(responseText);

                if (result.TryGetProperty("success", out var successProp) && successProp.GetBoolean())
                {
                    // 获取支付链接
                    if (result.TryGetProperty("payment_url", out var urlProp))
                    {
                        string paymentUrl = urlProp.GetString() ?? "";
                        if (!string.IsNullOrEmpty(paymentUrl))
                        {
                            // 打开支付链接
                            Process.Start(new ProcessStartInfo
                            {
                                FileName = paymentUrl,
                                UseShellExecute = true
                            });

                            // await Tools.显示消息框Async("提示", "支付页面已打开，支付成功后请重新勾选加速功能", ButtonEnum.Ok, Icon.Info);
                        }
                    }
                }
                else
                {
                    string message = "购买失败";
                    if (result.TryGetProperty("message", out var messageProp))
                    {
                        message = messageProp.GetString() ?? "购买失败";
                    }

                    await Tools.显示消息框Async("错误", message, ButtonEnum.Ok, Icon.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 调用购买API失败: {ex.Message}");
                await Tools.显示消息框Async("错误", "网络连接失败，请稍后重试", ButtonEnum.Ok, Icon.Error);
            }
        }

        // 释放资源
        public void Dispose()
        {
            try
            {
                Debug.WriteLine("开始释放主要窗口VM资源...");

                // 关闭购买窗口实例（如果存在）
                try
                {
                    购买卡密VM.关闭购买窗口();
                    Debug.WriteLine("购买窗口已关闭");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"关闭购买窗口时出错: {ex.Message}");
                }

                Debug.WriteLine("主要窗口VM资源释放完成");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"释放主要窗口VM资源时出错: {ex.Message}");
            }
        }
    }
}
