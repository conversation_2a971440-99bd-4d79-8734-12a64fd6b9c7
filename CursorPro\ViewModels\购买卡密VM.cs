using Avalonia;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Threading;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CursorPro.Models;
using MsBox.Avalonia.Enums;
using System;
using System.Diagnostics;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace CursorPro.ViewModels
{
    public partial class 购买卡密VM : ViewModelBase
    {
        // 移除HttpClient实例
        private readonly string _apiBaseUrl;

        [ObservableProperty]
        private bool _isProcessing = false;

        [ObservableProperty]
        private bool _显示恢复购买按钮 = true;

        // 轮询相关属性
        private Timer? _轮询定时器;
        private string? _当前订单号;
        private int _轮询次数 = 0;
        private const int 最大轮询次数 = 100; // 5分钟 ÷ 3秒 = 100次
        private const int 轮询间隔毫秒 = 3000; // 3秒

        // 静态轮询实例，确保窗口关闭后轮询继续
        private static 购买卡密VM? _轮询实例;

        [ObservableProperty]
        private string _currentOrderNo = string.Empty;

        // 价格属性 - 简化设计：只保留原价和现价
        [ObservableProperty]
        private bool _价格加载中 = false;

        // 周卡价格
        [ObservableProperty]
        private string _周卡原价 = "9.9";

        [ObservableProperty]
        private string _周卡现价 = "9.9";

        // 月卡价格
        [ObservableProperty]
        private string _月卡原价 = "39.9";

        [ObservableProperty]
        private string _月卡现价 = "39.9";

        // 特惠状态（现价低于原价时为true）
        public bool 周卡有特惠 => double.TryParse(周卡现价, out double 现价) &&
                                double.TryParse(周卡原价, out double 原价) && 现价 < 原价;

        public bool 月卡有特惠 => double.TryParse(月卡现价, out double 现价) &&
                                double.TryParse(月卡原价, out double 原价) && 现价 < 原价;

        // 当价格属性变化时，通知特惠状态也可能变化
        partial void On周卡现价Changed(string value)
        {
            OnPropertyChanged(nameof(周卡有特惠));
            更新周卡总计信息(); // 价格变化时更新总计信息
        }

        partial void On周卡原价Changed(string value)
        {
            OnPropertyChanged(nameof(周卡有特惠));
        }

        partial void On月卡现价Changed(string value)
        {
            OnPropertyChanged(nameof(月卡有特惠));
            更新月卡总计信息(); // 价格变化时更新总计信息
        }

        partial void On月卡原价Changed(string value)
        {
            OnPropertyChanged(nameof(月卡有特惠));
        }

        // 数量选择相关属性
        [ObservableProperty]
        private int _周卡数量 = 1;

        [ObservableProperty]
        private int _月卡数量 = 1;

        [ObservableProperty]
        private string _周卡总计信息 = "总计: ¥9.9 (延长7天)";

        [ObservableProperty]
        private string _月卡总计信息 = "总计: ¥29.9 (延长30天)";

        // 本地订单存储的键名
        private const string 本地订单键名 = "pending_order_info";

        // 购买窗口单例管理
        private static Views.购买卡密? _购买窗口实例;

        public 购买卡密VM()
        {
            // 使用新的API基础地址和支付服务端口
            _apiBaseUrl = $"{Info.API基础地址}:{Info.支付服务端口}";

            // 初始化默认价格状态（月卡默认39.9，无特惠）
            初始化默认价格状态();

            // 检查本地卡密状态，决定是否显示恢复购买按钮
            检查恢复购买按钮可见性();

            // 异步加载价格
            _ = Task.Run(async () => await 加载价格());
        }

        /// <summary>
        /// 初始化默认价格状态
        /// </summary>
        private void 初始化默认价格状态()
        {
            // 周卡默认价格9.9，现价也是9.9（无特惠）
            周卡原价 = "9.9";
            周卡现价 = "9.9";

            // 月卡默认价格39.9，现价也是39.9（无特惠）
            月卡原价 = "39.9";
            月卡现价 = "29.9";

            // 初始化总计信息
            更新周卡总计信息();
            更新月卡总计信息();
        }

        /// <summary>
        /// 调整周卡数量
        /// </summary>
        [RelayCommand]
        private void 调整周卡数量(string 操作)
        {
            if (操作 == "增加" && 周卡数量 < 12) // 最多12张
                周卡数量++;
            else if (操作 == "减少" && 周卡数量 > 1)
                周卡数量--;

            更新周卡总计信息();
        }

        /// <summary>
        /// 调整月卡数量
        /// </summary>
        [RelayCommand]
        private void 调整月卡数量(string 操作)
        {
            if (操作 == "增加" && 月卡数量 < 12) // 最多12张
                月卡数量++;
            else if (操作 == "减少" && 月卡数量 > 1)
                月卡数量--;

            更新月卡总计信息();
        }

        /// <summary>
        /// 更新周卡总计信息
        /// </summary>
        private void 更新周卡总计信息()
        {
            if (decimal.TryParse(周卡现价, out decimal 单价))
            {
                decimal 总价 = 单价 * 周卡数量;
                int 总天数 = 7 * 周卡数量;
                周卡总计信息 = $"总计: ¥{总价} (延长{总天数}天)";
            }
        }

        /// <summary>
        /// 更新月卡总计信息
        /// </summary>
        private void 更新月卡总计信息()
        {
            if (decimal.TryParse(月卡现价, out decimal 单价))
            {
                decimal 总价 = 单价 * 月卡数量;
                int 总天数 = 30 * 月卡数量;
                月卡总计信息 = $"总计: ¥{总价} (延长{总天数}天)";
            }
        }

        /// <summary>
        /// 检查恢复购买按钮可见性
        /// </summary>
        private void 检查恢复购买按钮可见性()
        {
            try
            {
                // 检查本地是否有卡密
                string? 本地卡密 = LocalConfig.读取("CardKey");

                // 如果本地有卡密，则不显示恢复购买按钮
                显示恢复购买按钮 = string.IsNullOrEmpty(本地卡密);

                Debug.WriteLine($"[INFO] 本地卡密状态: {(string.IsNullOrEmpty(本地卡密) ? "无" : "有")}, 显示恢复购买按钮: {显示恢复购买按钮}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查恢复购买按钮可见性失败: {ex.Message}");
                // 出错时默认显示按钮
                显示恢复购买按钮 = true;
            }
        }

        /// <summary>
        /// 从服务端加载价格信息
        /// </summary>
        private async Task 加载价格()
        {
            try
            {
                // 使用付费服务端口获取价格
                var apiUrl = $"{Info.API基础地址}:{Info.付费服务端口}/api/prices";
                Debug.WriteLine($"正在从服务端获取价格: {apiUrl}");

                var 价格响应 = await HttpService.GetAsync<JsonDocument>(apiUrl);

                if (价格响应 != null)
                {
                    var rootElement = 价格响应.RootElement;

                    if (rootElement.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
                    {
                        if (rootElement.TryGetProperty("data", out var dataElement) &&
                            dataElement.TryGetProperty("prices", out var pricesElement))
                        {
                            // 解析价格数组
                            foreach (var priceItem in pricesElement.EnumerateArray())
                            {
                                if (priceItem.TryGetProperty("type", out var typeElement) &&
                                    priceItem.TryGetProperty("price", out var priceElement))
                                {
                                    string 卡片类型 = typeElement.GetString() ?? "";
                                    double 价格 = priceElement.GetDouble();

                                    // 更新对应的价格属性 - 简化逻辑
                                    switch (卡片类型)
                                    {
                                        case "周卡":
                                            // 周卡原价固定为9.9，现价从网络同步
                                            string 新周卡现价 = 价格.ToString("F1");

                                            // 只有当价格与当前显示的价格不同时才更新
                                            if (周卡现价 != 新周卡现价)
                                            {
                                                周卡现价 = 新周卡现价;
                                                // 原价保持不变，固定为9.9
                                                Debug.WriteLine($"周卡价格已更新: 原价={周卡原价}, 现价={新周卡现价}");
                                            }
                                            break;

                                        case "月卡":
                                            // 月卡原价固定为39.9，现价从网络同步
                                            string 新月卡现价 = 价格.ToString("F1");

                                            // 只有当价格与当前显示的价格不同时才更新
                                            if (月卡现价 != 新月卡现价)
                                            {
                                                月卡现价 = 新月卡现价;
                                                // 原价保持不变，固定为39.9
                                                Debug.WriteLine($"月卡价格已更新: 原价={月卡原价}, 现价={新月卡现价}");
                                            }
                                            break;
                                    }
                                }
                            }

                            Debug.WriteLine($"价格加载成功: 周卡现价={周卡现价}, 月卡现价={月卡现价}");
                        }
                    }
                    else
                    {
                        string 错误信息 = rootElement.TryGetProperty("message", out var messageElement)
                            ? messageElement.GetString() ?? "获取价格失败"
                            : "获取价格失败";
                        Debug.WriteLine($"服务端返回错误: {错误信息}");
                    }
                }
                else
                {
                    Debug.WriteLine("价格API返回空响应");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"加载价格失败: {ex.Message}");
                // 保持默认价格，不显示错误给用户
            }
        }

        [RelayCommand]
        private async Task 选择卡密(string 类型)
        {
            try
            {
                IsProcessing = true;

                // 获取对应的数量
                int 购买数量 = 类型 == "周卡" ? 周卡数量 : 月卡数量;

                // 获取机器码（如果Info中已有则使用，否则生成）
                string 机器码 = string.IsNullOrEmpty(Info.机器码) ? new 获取设备标识().获取机器码() : Info.机器码;

                // 如果机器码为空，先生成并存储到Info
                if (string.IsNullOrEmpty(Info.机器码))
                {
                    Info.机器码 = 机器码;
                }

                // 检查是否是续卡操作 - 通过本地是否有卡密来判断
                string? 本地卡密 = LocalConfig.读取("CardKey");
                bool 是续卡 = !string.IsNullOrEmpty(本地卡密);

                // 调用API创建支付链接，如果是续卡则使用续卡API
                Debug.WriteLine($"用户选择了{购买数量}张{类型}，是否续卡：{是续卡}，本地卡密：{(string.IsNullOrEmpty(本地卡密) ? "无" : "有")}");
                var 支付结果 = 是续卡
                    ? await 创建续费链接(类型, 机器码, 购买数量)
                    : await 创建支付链接(类型, 机器码, 购买数量);

                if (支付结果 != null)
                {
                    JsonElement rootElement = 支付结果.Value;

                    if (rootElement.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
                    {
                        // 保存订单号，以便后续查询
                        if (rootElement.TryGetProperty("order_no", out var orderNoElement))
                        {
                            CurrentOrderNo = orderNoElement.GetString() ?? string.Empty;
                        }

                        // 获取支付链接
                        if (rootElement.TryGetProperty("payment_url", out var paymentUrlElement))
                        {
                            string 支付链接 = paymentUrlElement.GetString() ?? string.Empty;
                            if (!string.IsNullOrEmpty(支付链接))
                            {
                                // 保存订单信息到本地（覆盖之前的订单）
                                保存订单到本地(CurrentOrderNo, 类型, 机器码, 是续卡);

                                // 启动轮询任务
                                启动订单轮询(CurrentOrderNo);

                                // 打开浏览器进行支付
                                打开浏览器(支付链接);

                                // 发起支付后关闭购买窗口
                                关闭购买窗口();

                                // 提示用户支付完成后点击一键获取
                                // await Tools.显示消息框Async("支付提示", "请完成支付后，点击主界面的\"一键获取额度\"按钮来获取激活信息。", ButtonEnum.Ok, Icon.Info);
                            }
                            else
                            {
                                await Tools.显示消息框Async("错误", "支付链接无效，请稍后重试。", ButtonEnum.Ok, Icon.Error);
                            }
                        }
                        else
                        {
                            await Tools.显示消息框Async("错误", "获取支付链接失败，请稍后重试。", ButtonEnum.Ok, Icon.Error);
                        }
                    }
                    else
                    {
                        string 错误信息 = rootElement.TryGetProperty("message", out var messageElement)
                            ? messageElement.GetString() ?? "创建支付链接失败"
                            : "创建支付链接失败";
                        await Tools.显示消息框Async("创建失败", $"创建支付链接失败: {错误信息}", ButtonEnum.Ok, Icon.Error);
                    }
                }
                else
                {
                    // 创建支付链接失败：无法连接到服务器
                    await Tools.显示消息框Async("服务器错误", "无法连接到服务器，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
                }
            }
            catch (Exception ex)
            {
                // 购买过程中出现错误: 错误信息
                Debug.WriteLine($"购买过程中出现错误: {ex.Message}");
                await Tools.显示消息框Async("激活失败", "激活过程中出现错误，请稍后重试。", ButtonEnum.Ok, Icon.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// 保存订单信息到本地配置
        /// </summary>
        private void 保存订单到本地(string 订单号, string 卡密类型, string 机器码, bool 是续卡)
        {
            try
            {
                var 订单信息 = new
                {
                    order_no = 订单号,
                    card_type = 卡密类型,
                    machine_code = 机器码,
                    is_renewal = 是续卡,
                    create_time = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                string 订单Json = JsonSerializer.Serialize(订单信息);
                LocalConfig.写入(本地订单键名, 订单Json);

                Debug.WriteLine($"订单信息已保存到本地: {订单号}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"保存订单到本地失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查本地是否有待处理的订单
        /// </summary>
        public static bool 检查本地订单()
        {
            try
            {
                string? 订单Json = LocalConfig.读取(本地订单键名);
                if (string.IsNullOrEmpty(订单Json))
                {
                    return false;
                }

                // 检查订单是否超过2小时，如果超过则删除
                if (检查订单是否超时(订单Json))
                {
                    删除本地订单();
                    Debug.WriteLine("本地订单已超过2小时，已自动删除");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查本地订单失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查订单是否超过2小时
        /// </summary>
        private static bool 检查订单是否超时(string 订单Json)
        {
            try
            {
                var jsonDoc = JsonDocument.Parse(订单Json);
                var rootElement = jsonDoc.RootElement;

                if (rootElement.TryGetProperty("create_time", out var createTimeElement))
                {
                    string? 创建时间字符串 = createTimeElement.GetString();
                    if (!string.IsNullOrEmpty(创建时间字符串))
                    {
                        if (DateTime.TryParse(创建时间字符串, out DateTime 创建时间))
                        {
                            // 检查是否超过2小时
                            TimeSpan 时间差 = DateTime.Now - 创建时间;
                            return 时间差.TotalMinutes > 120;
                        }
                    }
                }

                // 如果无法解析创建时间，为了安全起见，认为没有超时
                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"检查订单超时失败: {ex.Message}");
                // 如果检查失败，为了安全起见，认为没有超时
                return false;
            }
        }

        /// <summary>
        /// 获取本地订单信息
        /// </summary>
        public static JsonElement? 获取本地订单()
        {
            try
            {
                string? 订单Json = LocalConfig.读取(本地订单键名);
                if (!string.IsNullOrEmpty(订单Json))
                {
                    var jsonDoc = JsonDocument.Parse(订单Json);
                    return jsonDoc.RootElement.Clone();
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取本地订单失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 删除本地订单信息
        /// </summary>
        public static void 删除本地订单()
        {
            try
            {
                LocalConfig.写入(本地订单键名, "");
                Debug.WriteLine("本地订单信息已删除");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"删除本地订单失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查并处理本地订单
        /// </summary>
        /// <returns>如果本地订单处理成功返回验证结果，否则返回null</returns>
        public async Task<验证结果?> 检查并处理本地订单()
        {
            try
            {
                var 本地订单 = 获取本地订单();
                if (本地订单 == null)
                {
                    Debug.WriteLine("本地订单信息为空");
                    return null;
                }

                var 订单信息 = 本地订单.Value;

                // 获取订单号
                string 订单号 = string.Empty;
                if (订单信息.TryGetProperty("order_no", out var orderNoElement))
                {
                    订单号 = orderNoElement.GetString() ?? string.Empty;
                }

                if (string.IsNullOrEmpty(订单号))
                {
                    Debug.WriteLine("本地订单号为空，但不删除订单，继续后续流程");
                    return null;
                }

                Debug.WriteLine($"检测到本地订单: {订单号}，尝试检查支付状态");

                try
                {
                    var 订单结果 = await 检测订单状态(订单号);

                    if (订单结果 != null)
                    {
                        var rootElement = 订单结果.Value;

                        if (rootElement.TryGetProperty("success", out var successElement) && successElement.GetBoolean())
                        {
                            if (rootElement.TryGetProperty("order_info", out var orderInfoElement))
                            {
                                // 检查订单是否已支付
                                bool 已支付 = false;
                                if (orderInfoElement.TryGetProperty("支付时间", out var 支付时间Element))
                                {
                                    string? 支付时间 = 支付时间Element.GetString();
                                    已支付 = !string.IsNullOrEmpty(支付时间);
                                }

                                if (已支付)
                                {
                                    // 支付成功，获取卡密信息
                                    string 卡密 = string.Empty;
                                    string 到期时间 = string.Empty;

                                    if (orderInfoElement.TryGetProperty("卡密", out var 卡密Element))
                                    {
                                        卡密 = 卡密Element.GetString() ?? string.Empty;
                                    }

                                    if (orderInfoElement.TryGetProperty("有效期", out var 到期时间Element))
                                    {
                                        到期时间 = 到期时间Element.GetString() ?? string.Empty;
                                    }

                                    if (!string.IsNullOrEmpty(卡密))
                                    {
                                        // 保存卡密到本地配置
                                        LocalConfig.写入("CardKey", 卡密);

                                        // 只有支付成功时才删除本地订单
                                        删除本地订单();

                                        // 显示成功消息
                                        await Tools.显示消息框Async("激活成功", "感谢您的支持！已成功解锁独享无限额度权益", ButtonEnum.Ok, Icon.Success);

                                        // 返回成功的验证结果
                                        return new 验证结果
                                        {
                                            Success = true,
                                            Message = "激活成功! 和cursor继续对话吧"
                                        };
                                    }
                                }
                                else
                                {
                                    // 订单未支付，记录日志但不阻止后续执行，也不删除本地订单
                                    Debug.WriteLine($"订单 {订单号} 尚未支付，保留本地订单，继续后续流程");
                                }
                            }
                        }
                        else
                        {
                            // 订单查询失败，记录日志但不阻止后续执行，也不删除本地订单
                            string 错误信息 = rootElement.TryGetProperty("message", out var messageElement)
                                ? messageElement.GetString() ?? "订单查询失败"
                                : "订单查询失败";

                            Debug.WriteLine($"订单状态查询失败: {错误信息}，保留本地订单，继续后续流程");
                        }
                    }
                    else
                    {
                        // 网络错误，记录日志但不阻止后续执行，也不删除本地订单
                        Debug.WriteLine("无法连接到服务器检查订单状态，保留本地订单，继续后续流程");
                    }
                }
                catch (Exception ex)
                {
                    // 检测订单状态失败，记录日志但不阻止后续执行，也不删除本地订单
                    Debug.WriteLine($"检测订单状态失败: {ex.Message}，保留本地订单，继续后续流程");
                }
            }
            catch (Exception ex)
            {
                // 处理本地订单失败，记录日志但不阻止后续执行
                Debug.WriteLine($"处理本地订单失败: {ex.Message}，继续后续流程");
            }

            return null;
        }

        private async Task<JsonElement?> 创建支付链接(string 卡密类型, string 机器码, int 数量 = 1)
        {
            try
            {
                // 创建请求数据
                var requestData = new
                {
                    card_type = 卡密类型,
                    machine_code = 机器码,
                    quantity = 数量,  // 新增数量参数
                    app_name = Info.AppName,
                    remark = Info.代理
                };

                // 发送请求使用HttpService
                var apiUrl = $"{_apiBaseUrl}/create_payment";
                Debug.WriteLine($"创建支付链接: {apiUrl}, 类型: {卡密类型}, 数量: {数量}");
                var jsonResponse = await HttpService.PostAsync<object, JsonDocument>(apiUrl, requestData);

                if (jsonResponse != null)
                {
                    return jsonResponse.RootElement.Clone();
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建支付链接错误: {ex.Message}");
                // 错误弹窗提示
                await Tools.显示消息框Async("连接错误", "创建激活链接失败，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
                return null;
            }
        }

        /// <summary>
        /// 检测订单状态（不使用轮询）
        /// </summary>
        public async Task<JsonElement?> 检测订单状态(string 订单号)
        {
            try
            {
                // 创建请求数据
                var requestData = new
                {
                    order_no = 订单号
                };

                // 发送请求使用HttpService
                var apiUrl = $"{_apiBaseUrl}/query_order";
                var jsonResponse = await HttpService.PostAsync<object, JsonDocument>(apiUrl, requestData);

                if (jsonResponse != null)
                {
                    return jsonResponse.RootElement.Clone();
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"查询订单状态错误: {ex.Message}");
                throw; // 重新抛出异常，让调用者处理
            }
        }

        private void 打开浏览器(string url)
        {
            // 使用Tools类的OpenUrl方法替代
            Tools.OpenUrl(url);
        }

        /// <summary>
        /// 显示购买窗口（单例模式）
        /// </summary>
        public static void 显示购买窗口()
        {
            try
            {
                // 在UI线程上执行
                Dispatcher.UIThread.Post(() =>
                {
                    try
                    {
                        // 检查是否已有窗口实例且未关闭
                        if (_购买窗口实例 != null)
                        {
                            // 如果窗口已存在，将其激活并置于前台
                            _购买窗口实例.Activate();
                            _购买窗口实例.Topmost = true;
                            _购买窗口实例.Topmost = false; // 重置Topmost以避免一直置顶
                            Debug.WriteLine("购买窗口已存在，激活现有窗口");
                            return;
                        }

                        // 创建新的购买窗口实例
                        _购买窗口实例 = new Views.购买卡密();

                        // 监听窗口关闭事件，以便清理引用
                        _购买窗口实例.Closed += (sender, e) =>
                        {
                            Debug.WriteLine("购买窗口已关闭，清理实例引用");
                            _购买窗口实例 = null;
                        };

                        // 显示窗口
                        _购买窗口实例.Show();
                        Debug.WriteLine("创建并显示新的购买窗口");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"显示购买窗口时出错: {ex.Message}");
                        // 如果出错，清理可能损坏的实例引用
                        _购买窗口实例 = null;
                    }
                });
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"显示购买窗口时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭购买窗口（如果存在）
        /// </summary>
        public static void 关闭购买窗口()
        {
            try
            {
                if (_购买窗口实例 != null)
                {
                    Debug.WriteLine("检测到购买窗口已打开，正在关闭...");

                    // 不要停止轮询！轮询应该在后台继续运行
                    // 只有在激活成功或轮询超时时才停止轮询

                    _购买窗口实例.Close();
                    _购买窗口实例 = null;
                    Debug.WriteLine("购买窗口已关闭");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"关闭购买窗口时出错: {ex.Message}");
                // 确保清理引用，即使关闭失败
                _购买窗口实例 = null;
            }
        }

        private async Task<JsonElement?> 创建续费链接(string 卡密类型, string 机器码, int 数量 = 1)
        {
            try
            {
                // 从本地配置获取卡密
                string? 卡密 = LocalConfig.读取("CardKey");
                if (string.IsNullOrEmpty(卡密))
                {
                    Debug.WriteLine("续费失败：未找到本地卡密");
                    await Tools.显示消息框Async("续期失败", "无法找到当前使用的激活信息，请重新登录后再试。", ButtonEnum.Ok, Icon.Error);
                    return null;
                }

                // 创建请求数据
                var requestData = new
                {
                    card_key = 卡密,
                    card_type = 卡密类型,
                    machine_code = 机器码,
                    quantity = 数量,  // 新增数量参数
                    app_name = Info.AppName,
                    remark = Info.代理
                };

                // 发送请求使用HttpService
                Debug.WriteLine($"执行续费操作: 卡密={卡密}, 类型={卡密类型}, 数量={数量}, 机器码={机器码}");
                var apiUrl = $"{_apiBaseUrl}/create_renewal_payment";
                var jsonResponse = await HttpService.PostAsync<object, JsonDocument>(apiUrl, requestData);

                if (jsonResponse != null)
                {
                    JsonElement root = jsonResponse.RootElement;
                    // 记录服务器响应
                    if (root.TryGetProperty("success", out var success))
                    {
                        Debug.WriteLine($"续费API响应: success={success.GetBoolean()}");
                        if (!success.GetBoolean() && root.TryGetProperty("message", out var message))
                        {
                            Debug.WriteLine($"续费失败: {message.GetString()}");
                        }
                    }
                    return root.Clone();
                }
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"创建续费链接错误: {ex.Message}");
                // 错误弹窗提示
                await Tools.显示消息框Async("续期错误", "创建续期链接失败，请检查网络连接后重试。", ButtonEnum.Ok, Icon.Error);
                return null;
            }
        }

        /// <summary>
        /// 恢复购买记录命令
        /// </summary>
        [RelayCommand]
        private async Task 恢复购买记录()
        {
            try
            {
                // 防止重复点击
                if (IsProcessing)
                {
                    return;
                }

                // 弹出确认对话框
                var confirmResult = await Tools.显示确认消息框Async(
                    "恢复购买记录",
                    "将根据当前设备信息查找已购买的服务记录，是否继续？",
                    ButtonEnum.YesNo,
                    Icon.Question);

                if (confirmResult != ButtonResult.Yes)
                {
                    return;
                }

                IsProcessing = true;

                // 获取当前机器码
                var 设备标识 = new 获取设备标识();
                string 机器码 = 设备标识.获取机器码();

                if (string.IsNullOrEmpty(机器码))
                {
                    await Tools.显示消息框Async("错误", "无法获取设备标识信息", ButtonEnum.Ok, Icon.Error);
                    return;
                }

                // 调用恢复购买API
                var requestData = new { machine_code = 机器码 };
                var apiUrl = $"{Info.API基础地址}:{Info.付费服务端口}/api/restore_purchase";

                Debug.WriteLine($"[INFO] 调用恢复购买API: {apiUrl}");
                Debug.WriteLine($"[INFO] 机器码: {机器码.Substring(0, Math.Min(8, 机器码.Length))}***");

                var response = await HttpService.PostAsync<object, JsonDocument>(apiUrl, requestData);

                if (response != null)
                {
                    var rootElement = response.RootElement;

                    if (rootElement.TryGetProperty("success", out var successProp) && successProp.GetBoolean())
                    {
                        // 恢复成功
                        string cardKey = "";
                        string expiryTime = "";

                        if (rootElement.TryGetProperty("card_key", out var cardKeyProp))
                        {
                            cardKey = cardKeyProp.GetString() ?? "";
                        }

                        if (rootElement.TryGetProperty("expiry_time", out var expiryTimeProp))
                        {
                            expiryTime = expiryTimeProp.GetString() ?? "";
                        }

                        if (!string.IsNullOrEmpty(cardKey))
                        {
                            // 保存授权信息到本地配置
                            LocalConfig.写入("CardKey", cardKey);

                            if (!string.IsNullOrEmpty(expiryTime))
                            {
                                LocalConfig.写入("ExpiryTime", expiryTime);
                                Info.到期时间 = expiryTime;
                                Debug.WriteLine($"[INFO] 恢复购买成功，保存到期时间: {expiryTime}");
                            }

                            // 显示成功提示
                            await Tools.显示消息框Async("成功", "购买记录恢复成功！", ButtonEnum.Ok, Icon.Success);

                            // 通知主窗口刷新独享剩余时间
                            通知主窗口刷新状态();

                            // 关闭购买窗口
                            关闭窗口();

                            Debug.WriteLine($"[INFO] 购买记录恢复成功");
                        }
                        else
                        {
                            await Tools.显示消息框Async("错误", "服务器返回数据异常", ButtonEnum.Ok, Icon.Error);
                        }
                    }
                    else
                    {
                        // 恢复失败，显示服务器返回的错误信息
                        string message = "恢复购买记录失败";
                        if (rootElement.TryGetProperty("message", out var messageProp))
                        {
                            message = messageProp.GetString() ?? message;
                        }

                        await Tools.显示消息框Async("提示", message, ButtonEnum.Ok, Icon.Info);
                    }
                }
                else
                {
                    await Tools.显示消息框Async("错误", "服务器响应异常", ButtonEnum.Ok, Icon.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] 恢复购买记录失败: {ex.Message}");
                await Tools.显示消息框Async("错误", "网络连接失败，请稍后重试", ButtonEnum.Ok, Icon.Error);
            }
            finally
            {
                IsProcessing = false;
            }
        }

        /// <summary>
        /// 通知主窗口刷新状态
        /// </summary>
        private void 通知主窗口刷新状态()
        {
            try
            {
                // 通过Application.Current获取主窗口
                if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
                {
                    var mainWindow = desktop.MainWindow;
                    if (mainWindow?.DataContext is 主要窗口 mainViewModel)
                    {
                        Dispatcher.UIThread.Post(() =>
                        {
                            mainViewModel.更新使用类型文本();
                            Debug.WriteLine("[INFO] 已通知主窗口刷新独享剩余时间");
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"通知主窗口刷新状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭窗口
        /// </summary>
        private void 关闭窗口()
        {
            try
            {
                // 通过静态引用关闭窗口
                if (_购买窗口实例 != null)
                {
                    Dispatcher.UIThread.Post(() =>
                    {
                        _购买窗口实例.Close();
                        _购买窗口实例 = null;
                    });
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"关闭窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 启动订单轮询
        /// </summary>
        private void 启动订单轮询(string 订单号)
        {
            try
            {
                // 停止之前的轮询
                停止轮询();

                // 保存当前实例为轮询实例
                _轮询实例 = this;

                // 保存订单号和重置计数器
                _当前订单号 = 订单号;
                _轮询次数 = 0;

                Debug.WriteLine($"[INFO] 启动订单轮询: {订单号}");

                // 启动定时器，3秒后开始第一次轮询
                _轮询定时器 = new Timer(轮询回调, null, 轮询间隔毫秒, 轮询间隔毫秒);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"启动订单轮询失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 轮询回调方法
        /// </summary>
        private static async void 轮询回调(object? state)
        {
            try
            {
                // 检查轮询实例是否存在
                if (_轮询实例 == null)
                {
                    Debug.WriteLine("[WARN] 轮询实例为空，停止轮询");
                    return;
                }

                // 通过实例调用轮询逻辑
                await _轮询实例.执行轮询逻辑();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"轮询回调异常: {ex.Message}");
                // 异常时不停止轮询，继续下次尝试
            }
        }

        /// <summary>
        /// 执行轮询逻辑
        /// </summary>
        private async Task 执行轮询逻辑()
        {
            try
            {
                _轮询次数++;

                // 检查是否超过最大轮询次数
                if (_轮询次数 > 最大轮询次数)
                {
                    Debug.WriteLine($"[INFO] 轮询超时，已轮询{_轮询次数}次，停止轮询");
                    停止轮询();
                    return;
                }

                // 检查订单号是否有效
                if (string.IsNullOrEmpty(_当前订单号))
                {
                    Debug.WriteLine("[WARN] 订单号为空，停止轮询");
                    停止轮询();
                    return;
                }

                Debug.WriteLine($"[INFO] 第{_轮询次数}次轮询订单状态: {_当前订单号}");

                // 查询订单状态
                var 订单结果 = await 检测订单状态(_当前订单号);

                if (订单结果 != null)
                {
                    await 处理轮询结果(订单结果.Value);
                }
                else
                {
                    Debug.WriteLine("[WARN] 订单状态查询失败，继续轮询");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"执行轮询逻辑异常: {ex.Message}");
                // 异常时不停止轮询，继续下次尝试
            }
        }

        /// <summary>
        /// 处理轮询结果
        /// </summary>
        private async Task 处理轮询结果(JsonElement 订单结果)
        {
            try
            {
                if (订单结果.TryGetProperty("success", out var successProp) && successProp.GetBoolean())
                {
                    // 检查是否有order_info，如果有就说明订单已支付
                    if (订单结果.TryGetProperty("order_info", out var orderInfoProp))
                    {
                        // 订单已支付，停止轮询并自动激活
                        Debug.WriteLine("[INFO] 检测到订单已支付，开始自动激活");
                        停止轮询();

                        // 从order_info中获取卡密进行激活
                        if (orderInfoProp.ValueKind == JsonValueKind.Object)
                        {
                            // order_info是一个对象，需要解析其中的卡密字段
                            // 根据服务端代码，卡密字段名为"卡密"
                            if (orderInfoProp.TryGetProperty("卡密", out var cardKeyProp))
                            {
                                string 卡密 = cardKeyProp.GetString() ?? "";
                                if (!string.IsNullOrEmpty(卡密))
                                {
                                    await 自动激活卡密(卡密);
                                }
                                else
                                {
                                    Debug.WriteLine("[WARN] 订单已支付但卡密为空");
                                }
                            }
                            else
                            {
                                Debug.WriteLine("[WARN] 订单已支付但order_info中未找到卡密字段");
                                Debug.WriteLine($"[DEBUG] order_info内容: {orderInfoProp}");
                            }
                        }
                        else
                        {
                            Debug.WriteLine("[WARN] order_info不是对象类型");
                        }
                    }
                    else
                    {
                        // 订单未支付，继续轮询
                        Debug.WriteLine("[INFO] 订单尚未支付，继续轮询");
                    }
                }
                else
                {
                    // 查询失败，继续轮询
                    Debug.WriteLine("[WARN] 订单状态查询返回失败，继续轮询");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理轮询结果异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 自动激活卡密
        /// </summary>
        private async Task 自动激活卡密(string 卡密)
        {
            try
            {
                Debug.WriteLine($"[INFO] 开始自动激活卡密: {卡密.Substring(0, Math.Min(8, 卡密.Length))}***");

                // 调用激活API
                var 激活结果 = await 调用激活API(卡密);

                if (激活结果 != null && 激活结果.Success)
                {
                    // 激活成功，保存信息并通知用户
                    LocalConfig.写入("CardKey", 卡密);

                    if (!string.IsNullOrEmpty(激活结果.ExpiryTime))
                    {
                        LocalConfig.写入("ExpiryTime", 激活结果.ExpiryTime);
                        Info.到期时间 = 激活结果.ExpiryTime;
                    }

                    Debug.WriteLine("[INFO] 自动激活成功");

                    // 在UI线程显示成功提示
                    await Dispatcher.UIThread.InvokeAsync(async () =>
                    {
                        // 通知主窗口刷新状态
                        通知主窗口刷新状态();

                        // 显示激活成功提示（唯一的用户提示）
                        await Tools.显示消息框Async("激活成功", "您的服务已自动激活！", ButtonEnum.Ok, Icon.Success);

                        // 关闭购买窗口
                        关闭购买窗口();
                    });
                }
                else
                {
                    // 激活失败，静默处理
                    Debug.WriteLine($"[WARN] 自动激活失败: {激活结果?.Message ?? "未知错误"}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"自动激活卡密异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 调用激活API
        /// </summary>
        private async Task<验证结果?> 调用激活API(string 卡密)
        {
            try
            {
                var 检查限制 = new 检查限制();
                return await 检查限制.验证付费账号(卡密);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"调用激活API异常: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 停止轮询
        /// </summary>
        public void 停止轮询()
        {
            try
            {
                if (_轮询定时器 != null)
                {
                    _轮询定时器.Dispose();
                    _轮询定时器 = null;
                    Debug.WriteLine("[INFO] 轮询已停止");
                }

                _当前订单号 = null;
                _轮询次数 = 0;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"停止轮询异常: {ex.Message}");
            }
        }
    }
}