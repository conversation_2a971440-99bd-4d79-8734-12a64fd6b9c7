<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:CursorPro.ViewModels"
        mc:Ignorable="d" d:DesignWidth="650" d:DesignHeight="520"
        x:Class="CursorPro.Views.主要窗口"
        x:DataType="vm:主要窗口"
        Width="340" Height="400"
        WindowStartupLocation="CenterScreen"
        CanResize="False"
        Title="{Binding 窗口标题}">
        <Window.Background>
            <SolidColorBrush Color="White"/>
        </Window.Background>

        <!-- 添加窗口阴影效果 -->
        <Window.Effect>
            <DropShadowEffect BlurRadius="20" Opacity="0.4" OffsetX="0" OffsetY="4" Color="Black"/>
        </Window.Effect>

    <Design.DataContext>
        <!-- This only sets the DataContext for the previewer in an IDE,
             to set the actual DataContext for runtime, set the DataContext property in code (look at App.axaml.cs) -->
        <vm:主要窗口/>
    </Design.DataContext>



    <!--  主要内容  -->
        <StackPanel
                    Margin="15,10,15,15"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Orientation="Vertical"
                    Spacing="5">

            <!--  Logo图标（圆形）  -->
            <Border Width="60" 
                    Height="60" 
                    CornerRadius="30" 
                    ClipToBounds="True"
                    Margin="0,15,0,5"
                    HorizontalAlignment="Center">
                <Image Source="/Assets/织梦logo.png"
                       Stretch="UniformToFill"
                       Margin="-5"
                       Opacity="0.9"/>
                <Border.Effect>
                    <DropShadowEffect BlurRadius="12" Opacity="0.5" OffsetX="0" OffsetY="4" Color="Black"/>
                </Border.Effect>
            </Border>
            
            <!--  标题文字  -->
            <TextBlock Margin="0,0,0,10"
                      HorizontalAlignment="Center"
                      FontSize="23"
                      FontWeight="Bold"
                      Foreground="#3F51B5"
                      Text="CursorPro免费使用">
                <TextBlock.Effect>
                    <DropShadowEffect BlurRadius="4" Opacity="0.3" OffsetX="1" OffsetY="2" Color="Gray"/>
                </TextBlock.Effect>
            </TextBlock>

            <!-- 获取状态文本 - 不可点击 -->
            <TextBlock HorizontalAlignment="Center"
                      TextAlignment="Center"
                      FontSize="16"
                      Foreground="#4CAF50"
                      FontWeight="Bold"
                      Text="{Binding 获取状态文本}"
                      TextWrapping="Wrap"
                      MinHeight="20"
                      Margin="0,0,0,5"/>
                      
            <!-- 使用类型提示文本 (可点击) -->
            <Button Command="{Binding 状态文本点击Command}"
                   Background="Transparent"
                   BorderThickness="0"
                   Padding="0"
                   Cursor="Hand"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,0">
                <StackPanel Orientation="Horizontal" 
                        HorizontalAlignment="Center" 
                        Margin="0,0,0,0">
                    <!-- 左侧装饰 -->
                    <TextBlock Text="✦" 
                              FontSize="15" 
                              Foreground="#FF69B4" 
                              FontWeight="Bold" 
                              Margin="0,0,8,0"
                              VerticalAlignment="Center"/>
                    
                    <!-- 使用类型文本 -->
                    <Border Background="#FFF5F8"
                           CornerRadius="10"
                           Padding="8,3">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="Background" Duration="0:0:0.2"/>
                                <EffectTransition Property="Effect" Duration="0:0:0.2"/>
                            </Transitions>
                        </Border.Transitions>
                        <Border.Effect>
                            <DropShadowEffect BlurRadius="5" Opacity="0.2" OffsetX="0" OffsetY="1" Color="#FF69B4"/>
                        </Border.Effect>
                        <TextBlock HorizontalAlignment="Center"
                                  FontSize="14"
                                  Foreground="#FF69B4"
                                  FontWeight="Bold"
                                  Text="{Binding 使用类型文本}"
                                  MinHeight="20">
                            <TextBlock.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Foreground" Duration="0:0:0.2"/>
                                </Transitions>
                            </TextBlock.Transitions>
                        </TextBlock>
                    </Border>
                    
                    <!-- 右侧装饰 -->
                    <TextBlock Text="✦" 
                              FontSize="15" 
                              Foreground="#FF69B4" 
                              FontWeight="Bold" 
                              Margin="8,0,0,0"
                              VerticalAlignment="Center"/>
                </StackPanel>
                <Button.Styles>
                    <Style Selector="Button">
                        <Setter Property="Transitions">
                            <Transitions>
                                <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                            </Transitions>
                        </Setter>
                    </Style>
                    <Style Selector="Button:pointerover">
                        <Setter Property="RenderTransform" Value="scale(1.05)"/>
                    </Style>
                    <Style Selector="Button:pressed">
                        <Setter Property="RenderTransform" Value="scale(0.95)"/>
                    </Style>
                    <Style Selector="Button:pointerover Border">
                        <Setter Property="Background" Value="#FFE0F0"/>
                        <Setter Property="Effect">
                            <DropShadowEffect BlurRadius="10" Opacity="0.5" OffsetX="0" OffsetY="3" Color="#FF69B4"/>
                        </Setter>
                    </Style>
                    <Style Selector="Button:pointerover TextBlock">
                        <Setter Property="Foreground" Value="#D81B60"/>
                    </Style>
                    <Style Selector="Button:pressed TextBlock">
                        <Setter Property="Foreground" Value="#C2185B"/>
                    </Style>
                </Button.Styles>
            </Button>



            <!--  主要功能按钮区域  -->
            <Border Width="280"
                   Margin="0,5,0,0"
                   HorizontalAlignment="Center">
                <StackPanel Spacing="12">
                    <!-- 一键获取额度按钮 -->
                    <Button x:Name="SwitchAccountButton"
                            Width="200"
                            Height="42"
                            HorizontalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding 一键获取快速额度Command}"
                            Content="一键获取额度"
                            FontSize="15"
                            Background="#3F51B5"
                            Foreground="White"
                            BorderThickness="0"
                            CornerRadius="8">
                        <Button.Styles>
                            <Style Selector="Button:pointerover">
                                <Setter Property="RenderTransform" Value="scale(1.05)"/>
                                <Setter Property="Background" Value="#303F9F"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Style>
                            <Style Selector="Button:pressed">
                                <Setter Property="RenderTransform" Value="scale(0.95)"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Style>
                            <!-- 悬停时才显示阴影 -->
                            <Style Selector="Button:pointerover">
                                <Setter Property="Effect">
                                    <DropShadowEffect BlurRadius="8" Opacity="0.3" OffsetX="0" OffsetY="2"/>
                                </Setter>
                            </Style>
                        </Button.Styles>
                    </Button>



                    <!-- 使用教程按钮 -->
                    <Button x:Name="TutorialButton"
                            Width="200"
                            Height="42"
                            HorizontalAlignment="Center"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center"
                            Command="{Binding 使用教程Command}"
                            Content="使用教程"
                            FontSize="15"
                            Background="#BA68C8"
                            Foreground="White"
                            BorderThickness="0"
                            CornerRadius="8">
                        <Button.Styles>
                            <Style Selector="Button:pointerover">
                                <Setter Property="RenderTransform" Value="scale(1.05)"/>
                                <Setter Property="Background" Value="#9C27B0"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Style>
                            <Style Selector="Button:pressed">
                                <Setter Property="RenderTransform" Value="scale(0.95)"/>
                                <Setter Property="Foreground" Value="White"/>
                            </Style>
                            <!-- 悬停时才显示阴影 -->
                            <Style Selector="Button:pointerover">
                                <Setter Property="Effect">
                                    <DropShadowEffect BlurRadius="8" Opacity="0.3" OffsetX="0" OffsetY="2"/>
                                </Setter>
                            </Style>
                        </Button.Styles>
                    </Button>

                    <!-- 加速cursor复选框 -->
                    <CheckBox x:Name="AccelerateCursorCheckBox"
                             HorizontalAlignment="Center"
                             Margin="0,-7,0,5"
                             IsVisible="{Binding 显示加速Cursor功能}"
                             IsChecked="{Binding 加速Cursor已启用}"
                             Command="{Binding 加速Cursor切换Command}"
                             Width="120"
                             Height="22">
                        <TextBlock Text="突破锁区限制"
                                  FontSize="13"
                                  Foreground="#666666"
                                  VerticalAlignment="Center"/>

                        <!-- 复选框样式 -->
                        <CheckBox.Styles>
                            <Style Selector="CheckBox">
                                <Setter Property="Cursor" Value="Hand"/>
                                <Setter Property="Transitions">
                                    <Transitions>
                                        <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
                                    </Transitions>
                                </Setter>
                            </Style>

                            <Style Selector="CheckBox:pointerover">
                                <Setter Property="RenderTransform" Value="scale(1.02)"/>
                            </Style>

                            <Style Selector="CheckBox:pointerover TextBlock">
                                <Setter Property="Foreground" Value="#FF6B35"/>
                            </Style>

                            <Style Selector="CheckBox:checked TextBlock">
                                <Setter Property="Foreground" Value="#4CAF50"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                            </Style>
                        </CheckBox.Styles>
                    </CheckBox>

                    <!-- 版本信息链接 -->
                    <Button HorizontalAlignment="Center"
                            Command="{Binding 版本链接Command}"
                            Classes="link"
                            Background="Transparent"
                            BorderThickness="0"
                            Padding="5,2"
                            Margin="{Binding 版本链接边距}">
                        <StackPanel Orientation="Horizontal" Spacing="5">
                            <!-- 简单的下载图标 -->
                            <Canvas Width="12" Height="12" VerticalAlignment="Center">
                                <!-- 垂直线 -->
                                <Rectangle Fill="#757575" Width="1" Height="7" Canvas.Left="5.5" Canvas.Top="1"/>
                                <!-- 下载箭头 -->
                                <Polygon Fill="#757575" Points="6,8 3,5 9,5"/>
                                <!-- 底部横线 -->
                                <Rectangle Fill="#757575" Width="8" Height="1" Canvas.Left="2" Canvas.Top="10"/>
                            </Canvas>
                            <TextBlock Text="{Binding 版本号}"
                                      FontSize="15"
                                      Foreground="#757575"/>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Border>




        </StackPanel>

    <!-- 添加样式 -->
    <Window.Styles>
        <!-- 通用按钮样式 -->
        <Style Selector="Button">
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
                    <BrushTransition Property="Background" Duration="0:0:0.15"/>
                    <BrushTransition Property="BorderBrush" Duration="0:0:0.15"/>
                </Transitions>
            </Setter>
        </Style>

        <!-- 避免Default按钮样式的继承问题 -->
        <Style Selector="Button:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="{Binding $parent[Button].Background}"/>
            <Setter Property="TextElement.Foreground" Value="White"/>
        </Style>

        <!-- 主按钮样式 -->
        <Style Selector="Button.primary">
            <Setter Property="Background" Value="#3F51B5"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#3F51B5"/>
        </Style>

        <Style Selector="Button.primary:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#4F61C5"/>
            <Setter Property="TextElement.Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.primary:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#3545A5"/>
            <Setter Property="TextElement.Foreground" Value="White"/>
        </Style>

        <!-- 主按钮悬停效果 - 保持白色文字 -->
        <Style Selector="Button.primary:pointerover">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- 主按钮按下效果 - 保持白色文字 -->
        <Style Selector="Button.primary:pressed">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- 次要按钮样式 -->
        <Style Selector="Button.secondary">
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="Foreground" Value="#3F51B5"/>
            <Setter Property="BorderBrush" Value="#3F51B5"/>
        </Style>

        <Style Selector="Button.secondary:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#EEEEEE"/>
            <Setter Property="BorderBrush" Value="#4F61C5"/>
            <Setter Property="TextElement.Foreground" Value="#3F51B5"/>
        </Style>

        <Style Selector="Button.secondary:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#E0E0E0"/>
            <Setter Property="BorderBrush" Value="#3545A5"/>
            <Setter Property="TextElement.Foreground" Value="#3F51B5"/>
        </Style>

        <!-- 次要按钮悬停效果 - 保持蓝色文字 -->
        <Style Selector="Button.secondary:pointerover">
            <Setter Property="Foreground" Value="#3F51B5"/>
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- 次要按钮按下效果 - 保持蓝色文字 -->
        <Style Selector="Button.secondary:pressed">
            <Setter Property="Foreground" Value="#3F51B5"/>
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- 强调按钮样式 (红色背景) -->
        <Style Selector="Button.accent">
            <Setter Property="Background" Value="#E53935"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#E53935"/>
        </Style>

        <Style Selector="Button.accent:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#EF5350"/>
        </Style>

        <Style Selector="Button.accent:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#D32F2F"/>
        </Style>

        <Style Selector="Button.accent:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <Style Selector="Button.accent:pressed">
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- 绿色按钮样式 (用于付费功能) -->
        <Style Selector="Button.success">
            <Setter Property="Background" Value="#66BB6A"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderBrush" Value="#66BB6A"/>
        </Style>

        <Style Selector="Button.success:pointerover /template/ ContentPresenter">
            <Setter Property="Background" Value="#81C784"/>
            <Setter Property="TextElement.Foreground" Value="White"/>
        </Style>

        <Style Selector="Button.success:pressed /template/ ContentPresenter">
            <Setter Property="Background" Value="#4CAF50"/>
            <Setter Property="TextElement.Foreground" Value="White"/>
        </Style>

        <!-- 绿色按钮悬停效果 - 保持白色文字 -->
        <Style Selector="Button.success:pointerover">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
            <Setter Property="Effect">
                <DropShadowEffect BlurRadius="8" Opacity="0.3" OffsetX="0" OffsetY="2"/>
            </Setter>
        </Style>

        <!-- 绿色按钮按下效果 - 保持白色文字 -->
        <Style Selector="Button.success:pressed">
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- 链接按钮样式 -->
        <Style Selector="Button.link">
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="5,2"/>
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Foreground" Duration="0:0:0.2"/>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.15"/>
                </Transitions>
            </Setter>
        </Style>

        <!-- 链接按钮悬停效果 -->
        <Style Selector="Button.link:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.05)"/>
        </Style>



        <Style Selector="Button.link:pointerover PathIcon">
            <Setter Property="Foreground" Value="#3F51B5"/>
        </Style>

        <!-- 链接按钮按下效果 -->
        <Style Selector="Button.link:pressed">
            <Setter Property="RenderTransform" Value="scale(0.95)"/>
        </Style>

        <Style Selector="Button.link:pressed TextBlock">
            <Setter Property="Foreground" Value="#303F9F"/>
        </Style>

        <Style Selector="Button.link:pressed PathIcon">
            <Setter Property="Foreground" Value="#303F9F"/>
        </Style>




    </Window.Styles>
</Window>
