<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="using:CursorPro.ViewModels"
        mc:Ignorable="d" d:DesignWidth="750" d:DesignHeight="500"
        x:Class="CursorPro.Views.购买卡密"
        x:DataType="vm:购买卡密VM"
        Width="500" Height="450"
        WindowStartupLocation="CenterScreen"
        SystemDecorations="None"
        Background="Transparent"
        CanResize="False"
        Title="赞助支持">

    <Design.DataContext>
        <vm:购买卡密VM/>
    </Design.DataContext>

    <Window.Resources>
        <StreamGeometry x:Key="CloseIcon">M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z</StreamGeometry>
    </Window.Resources>

    <Window.Styles>
        <Style Selector="Button.CardBtn">
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="RenderTransform" Value="none"/>
            <Setter Property="Transitions">
                <Transitions>
                    <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2"/>
                </Transitions>
            </Setter>
            <Setter Property="Template">
                <ControlTemplate>
                    <Border CornerRadius="8"
                            BoxShadow="0 4 15 0 #40000000">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                                <GradientStop Color="#1565C0" Offset="0"/>
                                <GradientStop Color="#0D47A1" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Grid>
                            <ContentPresenter Content="{TemplateBinding Content}"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Margin="{TemplateBinding Padding}"/>
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter>
        </Style>

        <!-- 所有按钮的悬停效果 -->
        <Style Selector="Button.CardBtn:pointerover">
            <Setter Property="RenderTransform" Value="scale(1.02)"/>
        </Style>

        <!-- 所有按钮的按下效果 -->
        <Style Selector="Button.CardBtn:pressed">
            <Setter Property="RenderTransform" Value="scale(0.98)"/>
        </Style>

        <!-- 特性标签样式 -->
        <Style Selector="Border.FeatureTag">
            <Setter Property="Background" Value="#7C4DFF"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="BoxShadow" Value="0 2 5 0 #40000000"/>
        </Style>
    </Window.Styles>

    <Border CornerRadius="8"
            BoxShadow="0 10 30 0 #40000000"
            PointerPressed="Border_PointerPressed">
        <Border.Background>
            <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                <GradientStop Color="#F8FAFF" Offset="0"/>
                <GradientStop Color="#E8F2FF" Offset="1"/>
            </LinearGradientBrush>
        </Border.Background>

        <Grid>
            <!-- 添加关闭按钮 -->
            <Button HorizontalAlignment="Right"
                    VerticalAlignment="Top"
                    Margin="0,10,10,0"
                    Click="CloseButton_Click"
                    Background="#20000000"
                    BorderThickness="0"
                    Padding="6"
                    CornerRadius="15"
                    ZIndex="1">
                <PathIcon Data="{StaticResource CloseIcon}"
                         Height="10"
                         Width="10"
                         Foreground="Black"/>
            </Button>

            <StackPanel VerticalAlignment="Center" Margin="0,40,0,25">

                <!-- 标题 -->
                <TextBlock Text="Cursor无限额度，告别额度焦虑"
                         FontSize="17"
                         FontWeight="Bold"
                         Foreground="#4A90E2"
                         HorizontalAlignment="Center"
                         Margin="0,20,0,0"/>

                <!-- 卡密类型按钮 -->
                <Grid HorizontalAlignment="Center" Margin="0,40,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 周卡按钮 -->
                    <Button Grid.Column="0" Width="150" Height="135"
                            Command="{Binding 选择卡密Command}"
                            CommandParameter="周卡"
                            Classes="CardBtn">
                        <Grid Width="150" Height="135">
                            <!-- 今日特惠标签 - 右上角 -->
                            <Border Background="#FF6B35"
                                    CornerRadius="8"
                                    Padding="6,2"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Margin="0,2,2,0"
                                    IsVisible="{Binding 周卡有特惠}">
                                <TextBlock Text="今日特惠"
                                         FontSize="10"
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                            </Border>
                            <StackPanel VerticalAlignment="Center"
                                      HorizontalAlignment="Center">
                                <!-- 功能描述和时长 -->
                                <StackPanel Orientation="Horizontal"
                                          HorizontalAlignment="Center"
                                          Margin="0,0,0,4">
                                    <TextBlock Text="7天"
                                             FontSize="19"
                                             FontWeight="Bold"
                                             Foreground="White"
                                             Margin="0,0,6,0"/>
                                    <TextBlock Text="独享"
                                             FontSize="15"
                                             FontWeight="Bold"
                                             Foreground="#F0C14B"
                                             VerticalAlignment="Center"
                                             Margin="0,3,0,0"/>
                                </StackPanel>

                                <!-- 快速额度：无限 -->
                                <TextBlock Text="快速额度：无限"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Foreground="#FFD700"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,6"/>

                                <!-- 价格 -->
                                <StackPanel HorizontalAlignment="Center">
                                    <!-- 原价（划线价格） -->
                                    <StackPanel Orientation="Horizontal"
                                              HorizontalAlignment="Center"
                                              IsVisible="{Binding 周卡有特惠}">
                                        <TextBlock Text="￥"
                                                 FontSize="12"
                                                 Foreground="#CCCCCC"
                                                 VerticalAlignment="Bottom"
                                                 Margin="0,0,1,1"/>
                                        <TextBlock Text="{Binding 周卡原价}"
                                                 FontSize="12"
                                                 Foreground="#CCCCCC"
                                                 TextDecorations="Strikethrough"/>
                                    </StackPanel>

                                    <!-- 现价 -->
                                    <StackPanel Orientation="Horizontal"
                                              HorizontalAlignment="Center">
                                        <TextBlock Text="￥"
                                                 FontSize="15"
                                                 Foreground="White"
                                                 FontWeight="Bold"
                                                 VerticalAlignment="Bottom"
                                                 Margin="0,0,2,1"/>
                                        <TextBlock Text="{Binding 周卡现价}"
                                                 FontSize="15"
                                                 FontWeight="Bold"
                                                 Foreground="White"
                                                 IsVisible="{Binding !价格加载中}"/>
                                        <TextBlock Text="..."
                                                 FontSize="15"
                                                 FontWeight="Bold"
                                                 Foreground="White"
                                                 IsVisible="{Binding 价格加载中}"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Button>

                    <!-- 月卡按钮 -->
                    <Button Grid.Column="2" Width="150" Height="135"
                            Command="{Binding 选择卡密Command}"
                            CommandParameter="月卡"
                            Classes="CardBtn">
                        <Grid Width="150" Height="135">
                            <!-- 今日特惠标签 - 右上角 -->
                            <Border Background="#FF6B35"
                                    CornerRadius="8"
                                    Padding="6,2"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Margin="0,2,2,0"
                                    IsVisible="{Binding 月卡有特惠}">
                                <TextBlock Text="今日特惠"
                                         FontSize="10"
                                         FontWeight="Bold"
                                         Foreground="White"
                                         HorizontalAlignment="Center"/>
                            </Border>

                            <StackPanel VerticalAlignment="Center"
                                      HorizontalAlignment="Center">
                                <!-- 功能描述和时长 -->
                                <StackPanel Orientation="Horizontal"
                                          HorizontalAlignment="Center"
                                          Margin="0,0,0,4">
                                    <TextBlock Text="30天"
                                             FontSize="19"
                                             FontWeight="Bold"
                                             Foreground="White"
                                             Margin="0,0,6,0"/>
                                    <TextBlock Text="独享"
                                             FontSize="15"
                                             FontWeight="Bold"
                                             Foreground="#F0C14B"
                                             VerticalAlignment="Center"
                                             Margin="0,3,0,0"/>
                                </StackPanel>

                                <!-- 快速额度：无限 -->
                                <TextBlock Text="快速额度：无限"
                                         FontSize="15"
                                         FontWeight="Bold"
                                         Foreground="#FFD700"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,6"/>

                                <!-- 价格 -->
                                <StackPanel HorizontalAlignment="Center">
                                    <!-- 原价（划线价格） -->
                                    <StackPanel Orientation="Horizontal"
                                              HorizontalAlignment="Center"
                                              IsVisible="{Binding 月卡有特惠}">
                                        <TextBlock Text="￥"
                                                 FontSize="12"
                                                 Foreground="#CCCCCC"
                                                 VerticalAlignment="Bottom"
                                                 Margin="0,0,1,1"/>
                                        <TextBlock Text="{Binding 月卡原价}"
                                                 FontSize="12"
                                                 Foreground="#CCCCCC"
                                                 TextDecorations="Strikethrough"/>
                                    </StackPanel>

                                    <!-- 现价 -->
                                    <StackPanel Orientation="Horizontal"
                                              HorizontalAlignment="Center">
                                        <TextBlock Text="￥"
                                                 FontSize="15"
                                                 Foreground="White"
                                                 FontWeight="Bold"
                                                 VerticalAlignment="Bottom"
                                                 Margin="0,0,2,1"/>
                                        <TextBlock Text="{Binding 月卡现价}"
                                                 FontSize="15"
                                                 FontWeight="Bold"
                                                 Foreground="White"
                                                 IsVisible="{Binding !价格加载中}"/>
                                        <TextBlock Text="..."
                                                 FontSize="15"
                                                 FontWeight="Bold"
                                                 Foreground="White"
                                                 IsVisible="{Binding 价格加载中}"/>
                                    </StackPanel>
                                </StackPanel>
                            </StackPanel>
                        </Grid>
                    </Button>
                </Grid>

                <!-- 数量选择器区域 -->
                <Grid HorizontalAlignment="Center" Margin="0,-15,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="150"/>
                    </Grid.ColumnDefinitions>

                    <!-- 周卡数量选择器 -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="−" Width="30" Height="30" FontSize="16" FontWeight="Bold"
                                    Command="{Binding 调整周卡数量Command}" CommandParameter="减少"
                                    Background="#E8E8E8" Foreground="#666" BorderThickness="0" CornerRadius="6"/>
                            <TextBlock Text="{Binding 周卡数量}" Width="40" TextAlignment="Center"
                                       VerticalAlignment="Center" FontSize="16" FontWeight="Bold"
                                       Foreground="#333" Margin="10,0"/>
                            <Button Content="+" Width="30" Height="30" FontSize="14" FontWeight="Bold"
                                    Command="{Binding 调整周卡数量Command}" CommandParameter="增加"
                                    Background="#E8E8E8" Foreground="#666" BorderThickness="0" CornerRadius="6"/>
                        </StackPanel>
                    </StackPanel>

                    <!-- 月卡数量选择器 -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <Button Content="−" Width="30" Height="30" FontSize="16" FontWeight="Bold"
                                    Command="{Binding 调整月卡数量Command}" CommandParameter="减少"
                                    Background="#E8E8E8" Foreground="#666" BorderThickness="0" CornerRadius="6"/>
                            <TextBlock Text="{Binding 月卡数量}" Width="40" TextAlignment="Center"
                                       VerticalAlignment="Center" FontSize="16" FontWeight="Bold"
                                       Foreground="#333" Margin="10,0"/>
                            <Button Content="+" Width="30" Height="30" FontSize="14" FontWeight="Bold"
                                    Command="{Binding 调整月卡数量Command}" CommandParameter="增加"
                                    Background="#E8E8E8" Foreground="#666" BorderThickness="0" CornerRadius="6"/>
                        </StackPanel>
                    </StackPanel>
                </Grid>

                <!-- 特色功能横幅 -->
                <Border Background="Transparent"
                        CornerRadius="8"
                        Margin="30,0,30,0"
                        Padding="15,12">
                    <StackPanel Orientation="Vertical"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center">

                        <!-- 中间文字 -->
                        <TextBlock FontSize="17"
                                 Foreground="#1565C0"
                                 FontWeight="Bold"
                                 VerticalAlignment="Center"
                                 HorizontalAlignment="Center"
                                 TextAlignment="Center">
                            <Run Text="✨ 支持Claude3.7等模型无限次数对话 ✨" FontWeight="Bold"/>
                            <LineBreak/>
                            <LineBreak/>
                        </TextBlock>
                    </StackPanel>
                </Border>

                <!-- 恢复购买记录链接 -->
                <Button HorizontalAlignment="Center"
                        Margin="0,-15,0,0"
                        Background="Transparent"
                        BorderThickness="0"
                        Padding="8,4"
                        Command="{Binding 恢复购买记录Command}"
                        Cursor="Hand"
                        IsVisible="{Binding 显示恢复购买按钮}">
                    <TextBlock Text="恢复购买记录"
                             FontSize="12"
                             Foreground="#9CA3AF"
                             HorizontalAlignment="Center"/>
                </Button>
            </StackPanel>
        </Grid>
    </Border>
</Window>